<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { showToast } from "vant";

import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";
import LineSetting from "@/views/account/components/LineSetting.vue";
import { formatPhoneNumber } from "@/utils/core/tools";
import { PN_VERIFY_TYPE } from "@/components/ZVerifyDialog/types";
import { logout } from "@/api/user";

import { useGlobalStore } from "@/stores/global";
import { useKycMgrStore, KYC_STATUS } from "@/stores/kycMgr";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";

import { AutoPopMgr } from "@/utils/AutoPopMgr";
import ZVerifyDialog from "@/components/ZVerifyDialog/index.vue";
import SetChangePhoneDialog from "@/components/ZVerifyDialog/SetChangePhoneDialog.vue";
import SetLoginPasswordDialog from "@/components/ZVerifyDialog/SetLoginPasswordDialog.vue";
import SetPaymentPasswordDialog from "@/components/ZVerifyDialog/SetPaymentPasswordDialog.vue";
import VerifyDialogPreconditions from "@/components/ZVerifyDialog/VerifyDialogPreconditions.vue";
import { useVerifyPreconditions } from "@/composables/useVerifyPreconditions";

import GoogleLogo from "@/assets/icons/login/google-logo.svg";
import FacebookLogo from "@/assets/icons/login/facebook-logo.svg";
// ==================== Store 和路由 ====================
const globalStore = useGlobalStore();
const autoPopMgrStore = useAutoPopMgrStore();
const kycMgrStore = useKycMgrStore();
const router = useRouter();
const { userInfo } = storeToRefs(globalStore);
const { kycBonusInfo, kycStatus, showKycCompletedRewardTip } = storeToRefs(kycMgrStore);

// ==================== 弹窗状态管理 ====================
const dialogStates = ref({
  showUpdatePhone: false,
  showBindPhone: false,
  showInitLoginPassword: false,
  showUpdateLoginPassword: false,
  showInitPaymentPassword: false,
  showUpdatePaymentPassword: false,
  showLogout: false,
});

// 使用验证前置条件的 composable
const { showVerifyDialogPreconditions, handlePreconditionsConfirm } = useVerifyPreconditions();

// ==================== 计算属性 ====================
const kycStatusConfig = computed(() => {
  const statusIndex = kycStatus.value ?? 0;
  const statusTexts = ["Not Verified", "Completed", "Reviewing", "Rejected"];
  const statusColors = ["#999999", "#01D46A", "#FF936F", "#AC1140"];

  return {
    text: statusTexts[statusIndex] || "",
    color: statusColors[statusIndex] || "#999999",
  };
});

const phoneDisplayValue = computed(() => {
  return userInfo.value.phone ? formatPhoneNumber(userInfo.value.phone) : "Not Set";
});

const loginPasswordDisplayValue = computed(() => {
  return userInfo.value.login_password ? "Modify" : "Not Set";
});

// const paymentPasswordDisplayValue = computed(() => {
//   return userInfo.value.withdraw_password ? "Modify" : "Not Set";
// });

const isMiniChannel = computed(() => {
  return ["gcash", "maya"].includes(globalStore.channel?.toLowerCase());
});

// ==================== 数据获取 ====================

const init = async () => {
  kycMgrStore.fetchKycStatus;
  kycMgrStore.fetchKycBonus();
};

// ==================== 事件处理函数 ====================
const handleKycBtn = () => {
  if (kycStatus.value === KYC_STATUS.COMPLETE || kycStatus.value === KYC_STATUS.REVIEWING) {
    router.push(`/kyc/detail`);
  } else {
    kycMgrStore.checkPreconditions();
  }
};

const handleUpdatePhoneBtn = () => {
  if (userInfo.value.phone) {
    dialogStates.value.showUpdatePhone = true;
  } else {
    dialogStates.value.showBindPhone = true;
  }
};

const handleUpdateLoginPasswordBtn = () => {
  if (!userInfo.value.phone) {
    showToast("For safety of your account, before set login password, you must set phone number.");
    return;
  }
  if (userInfo.value.login_password) {
    dialogStates.value.showUpdateLoginPassword = true;
  } else {
    dialogStates.value.showInitLoginPassword = true;
  }
};

// const handleUpdatePaymentPasswordBtn = () => {
//   if (userInfo.value.withdraw_password) {
//     dialogStates.value.showUpdatePaymentPassword = true;
//   } else {
//     dialogStates.value.showInitPaymentPassword = true;
//   }
// };

const handleLogOut = async () => {
  await logout();
  // 重置弹窗状态，准备显示弹窗
  autoPopMgrStore.hasPop = false;
  AutoPopMgr.resetAllPopups();
  globalStore.loginOut();
};

// ==================== 弹窗关闭处理 ====================
const closeDialog = (dialogName: keyof typeof dialogStates.value) => {
  dialogStates.value[dialogName] = false;
};
</script>

<template>
  <ZPage
    :request="init"
    :backgroundColor="'transparent'"
    :narBarStyle="{ background: 'transparent' }"
  >
    <div v-if="isMiniChannel">
      <div class="security-center">
        <div class="scroll-content">
          <div class="setting-title">General Setting</div>
          <div class="setting-content">
            <LineSetting
              @click="handleKycBtn"
              text="Personal Details"
              :value="kycStatusConfig.text"
            >
              <template #icon>
                <ZIcon type="icon-kyc" />
              </template>
            </LineSetting>
            <LineSetting
              text="Version No."
              :rightText="ALL_APP_SOURCE_CONFIG.app_version"
              :showArrow="false"
            >
              <template #icon>
                <ZIcon type="icon-version" />
              </template>
            </LineSetting>
          </div>
        </div>
      </div>
    </div>
    <div class="security-center" v-else>
      <div class="scroll-content">
        <div class="setting-title">General Setting</div>
        <div class="setting-content">
          <LineSetting @click="handleUpdatePhoneBtn" :value="phoneDisplayValue" text="Phone">
            <template #icon>
              <ZIcon type="icon-phone" />
            </template>
          </LineSetting>
          <LineSetting
            @click="handleUpdateLoginPasswordBtn"
            :value="loginPasswordDisplayValue"
            text="Login Password"
          >
            <template #icon>
              <ZIcon type="icon-loginpwd" />
            </template>
          </LineSetting>
          <!-- <LineSetting
            @click="handleUpdatePaymentPasswordBtn"
            :value="paymentPasswordDisplayValue"
            text="Payment Password"
          >
            <template #icon>
              <ZIcon class="icon-paypwd" />
            </template>
          </LineSetting> -->
          <LineSetting @click="handleKycBtn" text="KYC Details" :value="kycStatusConfig.text">
            <template #icon>
              <ZIcon type="icon-kyc" />
            </template>
            <template #rightContent v-if="showKycCompletedRewardTip">
              <span class="security-center-kycStatus"
                >Get ₱{{ kycBonusInfo.kyc_completed_reward }} Now!</span
              >
            </template>
          </LineSetting>
        </div>
        <div class="setting-title">Link</div>
        <div class="setting-content">
          <LineSetting
            @click="handleUpdateLoginPasswordBtn"
            :value="loginPasswordDisplayValue"
            text="Link With Google"
          >
            <template #icon>
              <span class="line-icon">
                <GoogleLogo />
              </span>
            </template>
          </LineSetting>
          <LineSetting
            @click="handleUpdateLoginPasswordBtn"
            :value="loginPasswordDisplayValue"
            text="Link With Facebook"
          >
            <template #icon>
              <span class="line-icon">
                <FacebookLogo />
              </span>
            </template>
          </LineSetting>
        </div>
        <div class="setting-title">Account actions</div>
        <div class="setting-content">
          <LineSetting @click="dialogStates.showLogout = true" text="Logout">
            <template #icon>
              <ZIcon type="icon-Logout" />
            </template>
          </LineSetting>
          <LineSetting
            text="Version No."
            :rightText="ALL_APP_SOURCE_CONFIG.app_version"
            :showArrow="false"
          >
            <template #icon>
              <ZIcon type="icon-version" />
            </template>
          </LineSetting>
        </div>
      </div>
    </div>
    <!-- 更改手机号 -->
    <ZVerifyDialog
      v-model:showDialog="dialogStates.showUpdatePhone"
      :verifyType="PN_VERIFY_TYPE.ChangePhoneNumber"
      :succCallBack="() => closeDialog('showUpdatePhone')"
    />

    <!-- 绑定手机号 -->
    <SetChangePhoneDialog
      v-model:showNextDialog="dialogStates.showBindPhone"
      :verifyType="PN_VERIFY_TYPE.SetPhoneNumber"
      :succCallBack="() => closeDialog('showBindPhone')"
    />

    <!-- 更新登陆密码 -->
    <ZVerifyDialog
      v-model:showDialog="dialogStates.showUpdateLoginPassword"
      :verifyType="PN_VERIFY_TYPE.ForgetPassword"
      :succCallBack="() => closeDialog('showUpdateLoginPassword')"
    />

    <!-- 首次设置登陆密码 -->
    <SetLoginPasswordDialog
      v-model:showNextDialog="dialogStates.showInitLoginPassword"
      :succCallBack="() => closeDialog('showInitLoginPassword')"
    />

    <!-- 更新支付密码 -->
    <ZVerifyDialog
      v-model:showDialog="dialogStates.showUpdatePaymentPassword"
      :verifyType="PN_VERIFY_TYPE.ChangePaymentPassword"
      :succCallBack="() => closeDialog('showUpdatePaymentPassword')"
    />

    <!-- 首次支付密码 -->
    <SetPaymentPasswordDialog
      v-model:showNextDialog="dialogStates.showInitPaymentPassword"
      :verifyType="PN_VERIFY_TYPE.SetPaymentPassword"
      @complete="closeDialog('showInitPaymentPassword')"
    />
    <!-- 绑定手机号、登录密码 -->
    <VerifyDialogPreconditions
      v-model:showDialog="showVerifyDialogPreconditions"
      :succCallBack="handlePreconditionsConfirm"
    >
    </VerifyDialogPreconditions>
    <!-- 退出登陆 -->
    <ZActionSheet
      v-model="dialogStates.showLogout"
      title="Tips"
      :showCancelButton="false"
      confirmText="Log Out"
      :onConfirm="handleLogOut"
    >
      Confirm to log out?
    </ZActionSheet>
  </ZPage>
</template>

<style scoped lang="scss">
.security-center {
  font-family: "Inter";
  height: 100%;
  background-color: #f4f8fb;

  .icon-version {
    display: inline-block;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    background-color: #97acff1a;
  }

  .scroll-content {
    color: rgba(48, 48, 48, 1);
    // --优化 高度
    overflow-y: auto;

    .setting-title {
      font-size: 14px;
      color: #999;
      padding: 10px 0 6px 10px;
      color: #999;
    }

    .setting-content {
      background-color: #fff;
      margin: 10px 16px;
      box-sizing: border-box;
      padding: 8px;
      border-radius: 10px;

      .iconfont {
        font-size: 20px;
      }
      .line-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
        padding: 10px;
        background-color: #97acff1a;
        border-radius: 50%;

        :deep(svg) {
          width: 20px !important;
          height: 20px !important;
          max-width: 20px !important;
          max-height: 20px !important;
        }

        // 更具体的选择器，针对Facebook Logo
        :deep(svg[width="38"]) {
          width: 20px !important;
          height: 20px !important;
        }
      }
    }
  }
}

.security-center-kycStatus {
  font-family: "Inter";
  font-weight: 500;
  font-size: 12px;
  letter-spacing: 0px;
  text-align: center;
  color: #ac1140;
  margin-left: 30px;
  white-space: nowrap;
}
</style>
